import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { 
  Building2, 
  Plus, 
  Edit, 
  Trash2, 
  Layers,
  Home,
  ArrowLeft,
  Save,
  X
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { 
  mockHostels, 
  mockFloors, 
  mockRooms,
  Floor,
  Room,
  getFloorsByHostelId,
  getRoomsByHostelId,
  getRoomsByFloorId
} from '@/data/mockData';

const availableAmenities = [
  'Reception', 'Common Room', 'Study Room', 'Laundry Room', 'Dining Area',
  'Lounge', 'Cafeteria', 'Gym', 'Library', 'TV Room', 'Gaming Zone',
  'Meeting Room', 'Prayer Room', 'Medical Room', 'Storage Room'
];

export const FloorRoomManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  
  const hostelId = searchParams.get('hostelId');
  const defaultTab = searchParams.get('tab') || 'floors';
  
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [selectedHostel, setSelectedHostel] = useState<string>(hostelId || '');
  const [isFloorDialogOpen, setIsFloorDialogOpen] = useState(false);
  const [isRoomDialogOpen, setIsRoomDialogOpen] = useState(false);
  const [editingFloor, setEditingFloor] = useState<Floor | null>(null);
  const [editingRoom, setEditingRoom] = useState<Room | null>(null);

  // Get owner's approved hostels
  const ownerHostels = mockHostels.filter(hostel => 
    hostel.ownerId === user?.id && hostel.approvalStatus === 'approved'
  );

  // Get floors and rooms for selected hostel
  const hostelFloors = selectedHostel ? getFloorsByHostelId(selectedHostel) : [];
  const hostelRooms = selectedHostel ? getRoomsByHostelId(selectedHostel) : [];

  // Floor form state
  const [floorForm, setFloorForm] = useState({
    floorNumber: '',
    floorName: '',
    description: '',
    amenities: [] as string[]
  });

  // Room form state
  const [roomForm, setRoomForm] = useState({
    floorId: '',
    roomNumber: '',
    roomType: 'double' as Room['roomType'],
    capacity: 2,
    pricePerBed: 0,
    amenities: [] as string[],
    description: '',
    furnishing: [] as string[],
    dimensions: {
      length: 0,
      width: 0,
      area: 0
    }
  });

  useEffect(() => {
    if (hostelId && ownerHostels.find(h => h.id === hostelId)) {
      setSelectedHostel(hostelId);
    }
  }, [hostelId, ownerHostels]);

  const resetFloorForm = () => {
    setFloorForm({
      floorNumber: '',
      floorName: '',
      description: '',
      amenities: []
    });
    setEditingFloor(null);
  };

  const resetRoomForm = () => {
    setRoomForm({
      floorId: '',
      roomNumber: '',
      roomType: 'double',
      capacity: 2,
      pricePerBed: 0,
      amenities: [],
      description: '',
      furnishing: [],
      dimensions: { length: 0, width: 0, area: 0 }
    });
    setEditingRoom(null);
  };

  const handleFloorSubmit = async () => {
    if (!selectedHostel || !floorForm.floorNumber || !floorForm.floorName) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const newFloor: Floor = {
        id: editingFloor?.id || `floor-${Date.now()}`,
        hostelId: selectedHostel,
        floorNumber: parseInt(floorForm.floorNumber),
        floorName: floorForm.floorName,
        totalRooms: editingFloor?.totalRooms || 0,
        description: floorForm.description,
        amenities: floorForm.amenities,
        createdDate: editingFloor?.createdDate || new Date().toISOString().split('T')[0],
        updatedDate: new Date().toISOString().split('T')[0]
      };

      if (editingFloor) {
        // Update existing floor
        const index = mockFloors.findIndex(f => f.id === editingFloor.id);
        if (index !== -1) {
          mockFloors[index] = newFloor;
        }
        toast({
          title: 'Floor Updated',
          description: 'Floor has been updated successfully.',
        });
      } else {
        // Add new floor
        mockFloors.push(newFloor);
        toast({
          title: 'Floor Added',
          description: 'New floor has been added successfully.',
        });
      }

      setIsFloorDialogOpen(false);
      resetFloorForm();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save floor. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleRoomSubmit = async () => {
    if (!selectedHostel || !roomForm.floorId || !roomForm.roomNumber) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const newRoom: Room = {
        id: editingRoom?.id || `room-${Date.now()}`,
        hostelId: selectedHostel,
        floorId: roomForm.floorId,
        roomNumber: roomForm.roomNumber,
        roomType: roomForm.roomType,
        capacity: roomForm.capacity,
        currentOccupancy: editingRoom?.currentOccupancy || 0,
        pricePerBed: roomForm.pricePerBed,
        amenities: roomForm.amenities,
        description: roomForm.description,
        images: editingRoom?.images || [],
        status: editingRoom?.status || 'available',
        dimensions: roomForm.dimensions,
        furnishing: roomForm.furnishing,
        createdDate: editingRoom?.createdDate || new Date().toISOString().split('T')[0],
        updatedDate: new Date().toISOString().split('T')[0]
      };

      if (editingRoom) {
        // Update existing room
        const index = mockRooms.findIndex(r => r.id === editingRoom.id);
        if (index !== -1) {
          mockRooms[index] = newRoom;
        }
        toast({
          title: 'Room Updated',
          description: 'Room has been updated successfully.',
        });
      } else {
        // Add new room
        mockRooms.push(newRoom);
        
        // Update floor's total rooms count
        const floor = mockFloors.find(f => f.id === roomForm.floorId);
        if (floor) {
          floor.totalRooms += 1;
        }
        
        toast({
          title: 'Room Added',
          description: 'New room has been added successfully.',
        });
      }

      setIsRoomDialogOpen(false);
      resetRoomForm();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save room. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteFloor = async (floorId: string) => {
    const floorRooms = getRoomsByFloorId(floorId);
    if (floorRooms.length > 0) {
      toast({
        title: 'Cannot Delete Floor',
        description: 'Please remove all rooms from this floor before deleting it.',
        variant: 'destructive',
      });
      return;
    }

    const index = mockFloors.findIndex(f => f.id === floorId);
    if (index !== -1) {
      mockFloors.splice(index, 1);
      toast({
        title: 'Floor Deleted',
        description: 'Floor has been deleted successfully.',
      });
    }
  };

  const handleDeleteRoom = async (roomId: string) => {
    const room = mockRooms.find(r => r.id === roomId);
    if (room && room.currentOccupancy > 0) {
      toast({
        title: 'Cannot Delete Room',
        description: 'This room is currently occupied. Please ensure it\'s empty before deleting.',
        variant: 'destructive',
      });
      return;
    }

    const index = mockRooms.findIndex(r => r.id === roomId);
    if (index !== -1) {
      const deletedRoom = mockRooms[index];
      mockRooms.splice(index, 1);
      
      // Update floor's total rooms count
      const floor = mockFloors.find(f => f.id === deletedRoom.floorId);
      if (floor && floor.totalRooms > 0) {
        floor.totalRooms -= 1;
      }
      
      toast({
        title: 'Room Deleted',
        description: 'Room has been deleted successfully.',
      });
    }
  };

  const editFloor = (floor: Floor) => {
    setEditingFloor(floor);
    setFloorForm({
      floorNumber: floor.floorNumber.toString(),
      floorName: floor.floorName,
      description: floor.description || '',
      amenities: floor.amenities
    });
    setIsFloorDialogOpen(true);
  };

  const editRoom = (room: Room) => {
    setEditingRoom(room);
    setRoomForm({
      floorId: room.floorId,
      roomNumber: room.roomNumber,
      roomType: room.roomType,
      capacity: room.capacity,
      pricePerBed: room.pricePerBed,
      amenities: room.amenities,
      description: room.description || '',
      furnishing: room.furnishing,
      dimensions: room.dimensions || { length: 0, width: 0, area: 0 }
    });
    setIsRoomDialogOpen(true);
  };

  if (ownerHostels.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => navigate('/owner/hostels')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Hostels
          </Button>
        </div>
        
        <Card>
          <CardContent className="text-center py-12">
            <Building2 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Approved Hostels</h3>
            <p className="text-muted-foreground mb-6">
              You need to have approved hostels before you can manage floors and rooms.
            </p>
            <Button onClick={() => navigate('/owner/hostel-registration')}>
              <Plus className="h-4 w-4 mr-2" />
              Register New Hostel
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => navigate('/owner/hostels')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Hostels
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Floor & Room Management</h1>
            <p className="text-muted-foreground">
              Manage floors and rooms for your approved hostels
            </p>
          </div>
        </div>
      </div>

      {/* Hostel Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Hostel</CardTitle>
          <CardDescription>
            Choose a hostel to manage its floors and rooms
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select value={selectedHostel} onValueChange={setSelectedHostel}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a hostel" />
            </SelectTrigger>
            <SelectContent>
              {ownerHostels.map((hostel) => (
                <SelectItem key={hostel.id} value={hostel.id}>
                  {hostel.name} - {hostel.city}, {hostel.state}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {selectedHostel && (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="floors" className="flex items-center gap-2">
              <Layers className="h-4 w-4" />
              Floors ({hostelFloors.length})
            </TabsTrigger>
            <TabsTrigger value="rooms" className="flex items-center gap-2">
              <Home className="h-4 w-4" />
              Rooms ({hostelRooms.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="floors" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Floor Management</h2>
              <Dialog open={isFloorDialogOpen} onOpenChange={setIsFloorDialogOpen}>
                <DialogTrigger asChild>
                  <Button onClick={resetFloorForm}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Floor
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>
                      {editingFloor ? 'Edit Floor' : 'Add New Floor'}
                    </DialogTitle>
                    <DialogDescription>
                      {editingFloor ? 'Update floor information' : 'Create a new floor for your hostel'}
                    </DialogDescription>
                  </DialogHeader>
                  
                  <FloorForm 
                    form={floorForm}
                    setForm={setFloorForm}
                    onSubmit={handleFloorSubmit}
                    onCancel={() => {
                      setIsFloorDialogOpen(false);
                      resetFloorForm();
                    }}
                    isEditing={!!editingFloor}
                  />
                </DialogContent>
              </Dialog>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {hostelFloors.map((floor) => (
                <FloorCard 
                  key={floor.id}
                  floor={floor}
                  onEdit={editFloor}
                  onDelete={handleDeleteFloor}
                />
              ))}
            </div>

            {hostelFloors.length === 0 && (
              <Card>
                <CardContent className="text-center py-12">
                  <Layers className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Floors Added</h3>
                  <p className="text-muted-foreground mb-4">
                    Start by adding floors to organize your hostel rooms
                  </p>
                  <Button onClick={() => setIsFloorDialogOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add First Floor
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="rooms" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Room Management</h2>
              <Dialog open={isRoomDialogOpen} onOpenChange={setIsRoomDialogOpen}>
                <DialogTrigger asChild>
                  <Button 
                    onClick={resetRoomForm}
                    disabled={hostelFloors.length === 0}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Room
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>
                      {editingRoom ? 'Edit Room' : 'Add New Room'}
                    </DialogTitle>
                    <DialogDescription>
                      {editingRoom ? 'Update room information' : 'Create a new room in your hostel'}
                    </DialogDescription>
                  </DialogHeader>
                  
                  <RoomForm 
                    form={roomForm}
                    setForm={setRoomForm}
                    floors={hostelFloors}
                    onSubmit={handleRoomSubmit}
                    onCancel={() => {
                      setIsRoomDialogOpen(false);
                      resetRoomForm();
                    }}
                    isEditing={!!editingRoom}
                  />
                </DialogContent>
              </Dialog>
            </div>

            {hostelFloors.length === 0 ? (
              <Alert>
                <AlertDescription>
                  You need to add floors before you can create rooms. Please add floors first.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-6">
                {hostelFloors.map((floor) => {
                  const floorRooms = getRoomsByFloorId(floor.id);
                  return (
                    <Card key={floor.id}>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Layers className="h-5 w-5" />
                          {floor.floorName}
                          <Badge variant="secondary">
                            {floorRooms.length} rooms
                          </Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid gap-4 sm:gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                          {floorRooms.map((room) => (
                            <RoomCard 
                              key={room.id}
                              room={room}
                              onEdit={editRoom}
                              onDelete={handleDeleteRoom}
                            />
                          ))}
                        </div>
                        {floorRooms.length === 0 && (
                          <div className="text-center py-8 text-muted-foreground">
                            No rooms on this floor yet
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

// Floor Form Component
interface FloorFormProps {
  form: any;
  setForm: (form: any) => void;
  onSubmit: () => void;
  onCancel: () => void;
  isEditing: boolean;
}

const FloorForm: React.FC<FloorFormProps> = ({ form, setForm, onSubmit, onCancel, isEditing }) => {
  const handleAmenityToggle = (amenity: string) => {
    const updatedAmenities = form.amenities.includes(amenity)
      ? form.amenities.filter((a: string) => a !== amenity)
      : [...form.amenities, amenity];

    setForm({ ...form, amenities: updatedAmenities });
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="floorNumber">Floor Number *</Label>
          <Input
            id="floorNumber"
            type="number"
            placeholder="e.g., 1"
            value={form.floorNumber}
            onChange={(e) => setForm({ ...form, floorNumber: e.target.value })}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="floorName">Floor Name *</Label>
          <Input
            id="floorName"
            placeholder="e.g., Ground Floor"
            value={form.floorName}
            onChange={(e) => setForm({ ...form, floorName: e.target.value })}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          placeholder="Describe this floor..."
          value={form.description}
          onChange={(e) => setForm({ ...form, description: e.target.value })}
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label>Floor Amenities</Label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {availableAmenities.map((amenity) => (
            <div key={amenity} className="flex items-center space-x-2">
              <Checkbox
                id={amenity}
                checked={form.amenities.includes(amenity)}
                onCheckedChange={() => handleAmenityToggle(amenity)}
              />
              <Label htmlFor={amenity} className="text-sm font-normal">
                {amenity}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <Button variant="outline" onClick={onCancel}>
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button onClick={onSubmit}>
          <Save className="h-4 w-4 mr-2" />
          {isEditing ? 'Update Floor' : 'Add Floor'}
        </Button>
      </div>
    </div>
  );
};

// Floor Card Component
interface FloorCardProps {
  floor: Floor;
  onEdit: (floor: Floor) => void;
  onDelete: (floorId: string) => void;
}

const FloorCard: React.FC<FloorCardProps> = ({ floor, onEdit, onDelete }) => {
  const floorRooms = getRoomsByFloorId(floor.id);

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg">{floor.floorName}</CardTitle>
            <p className="text-sm text-muted-foreground">Floor {floor.floorNumber}</p>
          </div>
          <Badge variant="secondary">
            {floorRooms.length} rooms
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {floor.description && (
          <p className="text-sm text-muted-foreground">{floor.description}</p>
        )}

        {floor.amenities.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Amenities</p>
            <div className="flex flex-wrap gap-1">
              {floor.amenities.slice(0, 3).map((amenity) => (
                <Badge key={amenity} variant="outline" className="text-xs">
                  {amenity}
                </Badge>
              ))}
              {floor.amenities.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{floor.amenities.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

        <div className="flex gap-2 pt-2">
          <Button variant="outline" size="sm" onClick={() => onEdit(floor)}>
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDelete(floor.id)}
            disabled={floorRooms.length > 0}
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Delete
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Room Form Component
interface RoomFormProps {
  form: any;
  setForm: (form: any) => void;
  floors: Floor[];
  onSubmit: () => void;
  onCancel: () => void;
  isEditing: boolean;
}

const roomAmenities = [
  'AC', 'Heating', 'Attached Bathroom', 'Shared Bathroom', 'Balcony',
  'Study Table', 'Chair', 'Wardrobe', 'Bookshelf', 'Mini Fridge',
  'TV', 'WiFi', 'Fan', 'Window', 'Curtains'
];

const furnishingOptions = [
  'Bed', 'Mattress', 'Pillow', 'Bedsheet', 'Study Table', 'Chair',
  'Wardrobe', 'Bookshelf', 'Mirror', 'Dustbin', 'Sofa', 'Side Table'
];

const RoomForm: React.FC<RoomFormProps> = ({ form, setForm, floors, onSubmit, onCancel, isEditing }) => {
  const handleAmenityToggle = (amenity: string) => {
    const updatedAmenities = form.amenities.includes(amenity)
      ? form.amenities.filter((a: string) => a !== amenity)
      : [...form.amenities, amenity];

    setForm({ ...form, amenities: updatedAmenities });
  };

  const handleFurnishingToggle = (item: string) => {
    const updatedFurnishing = form.furnishing.includes(item)
      ? form.furnishing.filter((f: string) => f !== item)
      : [...form.furnishing, item];

    setForm({ ...form, furnishing: updatedFurnishing });
  };

  const updateDimensions = (field: string, value: number) => {
    const newDimensions = { ...form.dimensions, [field]: value };
    if (field === 'length' || field === 'width') {
      newDimensions.area = newDimensions.length * newDimensions.width;
    }
    setForm({ ...form, dimensions: newDimensions });
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Basic Information</h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="floorId">Floor *</Label>
            <Select value={form.floorId} onValueChange={(value) => setForm({ ...form, floorId: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select floor" />
              </SelectTrigger>
              <SelectContent>
                {floors.map((floor) => (
                  <SelectItem key={floor.id} value={floor.id}>
                    {floor.floorName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="roomNumber">Room Number *</Label>
            <Input
              id="roomNumber"
              placeholder="e.g., G-101"
              value={form.roomNumber}
              onChange={(e) => setForm({ ...form, roomNumber: e.target.value })}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="roomType">Room Type *</Label>
            <Select value={form.roomType} onValueChange={(value) => setForm({ ...form, roomType: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select room type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="single">Single</SelectItem>
                <SelectItem value="double">Double</SelectItem>
                <SelectItem value="triple">Triple</SelectItem>
                <SelectItem value="quad">Quad</SelectItem>
                <SelectItem value="dormitory">Dormitory</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="capacity">Capacity *</Label>
            <Input
              id="capacity"
              type="number"
              min="1"
              max="20"
              value={form.capacity}
              onChange={(e) => setForm({ ...form, capacity: parseInt(e.target.value) || 1 })}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="pricePerBed">Price per Bed (₹/month) *</Label>
            <Input
              id="pricePerBed"
              type="number"
              min="1000"
              value={form.pricePerBed}
              onChange={(e) => setForm({ ...form, pricePerBed: parseInt(e.target.value) || 0 })}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            placeholder="Describe this room..."
            value={form.description}
            onChange={(e) => setForm({ ...form, description: e.target.value })}
            rows={3}
          />
        </div>
      </div>

      {/* Dimensions */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Room Dimensions</h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="length">Length (feet)</Label>
            <Input
              id="length"
              type="number"
              min="0"
              step="0.1"
              value={form.dimensions.length}
              onChange={(e) => updateDimensions('length', parseFloat(e.target.value) || 0)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="width">Width (feet)</Label>
            <Input
              id="width"
              type="number"
              min="0"
              step="0.1"
              value={form.dimensions.width}
              onChange={(e) => updateDimensions('width', parseFloat(e.target.value) || 0)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="area">Area (sq ft)</Label>
            <Input
              id="area"
              type="number"
              value={form.dimensions.area}
              readOnly
              className="bg-muted"
            />
          </div>
        </div>
      </div>

      {/* Amenities */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Room Amenities</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {roomAmenities.map((amenity) => (
            <div key={amenity} className="flex items-center space-x-2">
              <Checkbox
                id={amenity}
                checked={form.amenities.includes(amenity)}
                onCheckedChange={() => handleAmenityToggle(amenity)}
              />
              <Label htmlFor={amenity} className="text-sm font-normal">
                {amenity}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Furnishing */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Furnishing</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {furnishingOptions.map((item) => (
            <div key={item} className="flex items-center space-x-2">
              <Checkbox
                id={item}
                checked={form.furnishing.includes(item)}
                onCheckedChange={() => handleFurnishingToggle(item)}
              />
              <Label htmlFor={item} className="text-sm font-normal">
                {item}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <Button variant="outline" onClick={onCancel}>
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button onClick={onSubmit}>
          <Save className="h-4 w-4 mr-2" />
          {isEditing ? 'Update Room' : 'Add Room'}
        </Button>
      </div>
    </div>
  );
};

// Room Card Component
interface RoomCardProps {
  room: Room;
  onEdit: (room: Room) => void;
  onDelete: (roomId: string) => void;
}

const RoomCard: React.FC<RoomCardProps> = ({ room, onEdit, onDelete }) => {
  const getStatusColor = (status: Room['status']) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'occupied':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow border-2">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg sm:text-xl font-bold mb-1">{room.roomNumber}</CardTitle>
            <p className="text-sm text-muted-foreground capitalize">
              {room.roomType} • {room.capacity} beds
            </p>
          </div>
          <Badge variant="outline" className={`${getStatusColor(room.status)} font-medium px-3 py-1`}>
            {room.status}
          </Badge>
        </div>

        {/* Occupancy Progress Bar */}
        <div className="mt-3 space-y-2">
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Occupancy</span>
            <span>{room.currentOccupancy} of {room.capacity} beds</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all ${
                room.currentOccupancy === room.capacity
                  ? 'bg-red-500'
                  : room.currentOccupancy > room.capacity * 0.5
                  ? 'bg-yellow-500'
                  : 'bg-green-500'
              }`}
              style={{ width: `${(room.currentOccupancy / room.capacity) * 100}%` }}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <span className="text-xs text-muted-foreground block mb-1">Monthly Rate</span>
            <p className="font-bold text-sm text-primary">₹{room.pricePerBed.toLocaleString()}/bed</p>
          </div>
          <div className="bg-gray-50 rounded-lg p-3">
            <span className="text-xs text-muted-foreground block mb-1">Occupancy Rate</span>
            <p className="font-bold text-sm">{Math.round((room.currentOccupancy / room.capacity) * 100)}%</p>
          </div>
        </div>

        {room.dimensions && room.dimensions.area > 0 && (
          <div className="text-sm">
            <span className="text-muted-foreground">Area:</span>
            <span className="font-medium ml-1">{room.dimensions.area} sq ft</span>
          </div>
        )}

        {room.amenities.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Amenities</p>
            <div className="flex flex-wrap gap-1">
              {room.amenities.slice(0, 3).map((amenity) => (
                <Badge key={amenity} variant="outline" className="text-xs">
                  {amenity}
                </Badge>
              ))}
              {room.amenities.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{room.amenities.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

        <div className="flex gap-2 pt-2">
          <Button variant="outline" size="sm" onClick={() => onEdit(room)}>
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDelete(room.id)}
            disabled={room.currentOccupancy > 0}
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Delete
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
