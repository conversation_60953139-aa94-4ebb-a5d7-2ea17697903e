import React from 'react';
import { 
  Building2, 
  Home, 
  Users, 
  Bed as BedIcon,
  ArrowUp,
  ArrowDown,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DroppableBedZone, RoomLayout } from './DroppableBedZone';
import { 
  Bed, 
  Room, 
  Floor, 
  getBedsByRoomId, 
  getRoomsByFloorId,
  getFloorsByHostelId,
  getRoomOccupancyStatus
} from '@/data/mockData';

interface DragDropFloorPlanProps {
  hostelId: string;
  onBedClick?: (bed: Bed) => void;
  className?: string;
}

export const DragDropFloorPlan: React.FC<DragDropFloorPlanProps> = ({
  hostelId,
  onBedClick,
  className = ''
}) => {
  const [expandedFloors, setExpandedFloors] = React.useState<Set<string>>(new Set());
  const [compactView, setCompactView] = React.useState(false);

  // Get hostel data
  const floors = getFloorsByHostelId(hostelId);

  const toggleFloorExpansion = (floorId: string) => {
    const newExpanded = new Set(expandedFloors);
    if (newExpanded.has(floorId)) {
      newExpanded.delete(floorId);
    } else {
      newExpanded.add(floorId);
    }
    setExpandedFloors(newExpanded);
  };

  const toggleAllFloors = () => {
    if (expandedFloors.size === floors.length) {
      setExpandedFloors(new Set());
    } else {
      setExpandedFloors(new Set(floors.map(f => f.id)));
    }
  };

  if (floors.length === 0) {
    return (
      <div className={`${className}`}>
        <Card>
          <CardContent className="text-center py-12">
            <Building2 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Floors Found</h3>
            <p className="text-muted-foreground mb-6">
              Please add floors and rooms to use the drag-and-drop allocation system.
            </p>
            <Button onClick={() => window.location.href = '/owner/floors-rooms'}>
              <Home className="h-4 w-4 mr-2" />
              Manage Floors & Rooms
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Floor Plan Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold">Floor Plan Layout</h3>
          <Badge variant="secondary" className="text-xs">
            {floors.length} floors
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCompactView(!compactView)}
          >
            {compactView ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
            {compactView ? 'Expand' : 'Compact'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={toggleAllFloors}
          >
            {expandedFloors.size === floors.length ? 'Collapse All' : 'Expand All'}
          </Button>
        </div>
      </div>

      {/* Floor Plan */}
      <div className="space-y-4">
        {floors.map((floor) => (
          <FloorPlanSection
            key={floor.id}
            floor={floor}
            isExpanded={expandedFloors.has(floor.id)}
            isCompact={compactView}
            onToggleExpansion={() => toggleFloorExpansion(floor.id)}
            onBedClick={onBedClick}
          />
        ))}
      </div>
    </div>
  );
};

// Floor Plan Section Component
interface FloorPlanSectionProps {
  floor: Floor;
  isExpanded: boolean;
  isCompact: boolean;
  onToggleExpansion: () => void;
  onBedClick?: (bed: Bed) => void;
}

const FloorPlanSection: React.FC<FloorPlanSectionProps> = ({
  floor,
  isExpanded,
  isCompact,
  onToggleExpansion,
  onBedClick
}) => {
  const rooms = getRoomsByFloorId(floor.id);
  
  // Calculate floor statistics
  const floorStats = rooms.reduce((stats, room) => {
    const beds = getBedsByRoomId(room.id);
    const occupancyStatus = getRoomOccupancyStatus(room.id);
    
    return {
      totalRooms: stats.totalRooms + 1,
      totalBeds: stats.totalBeds + occupancyStatus.totalBeds,
      occupiedBeds: stats.occupiedBeds + occupancyStatus.occupiedBeds,
      availableBeds: stats.availableBeds + occupancyStatus.availableBeds,
      maintenanceBeds: stats.maintenanceBeds + occupancyStatus.maintenanceBeds
    };
  }, {
    totalRooms: 0,
    totalBeds: 0,
    occupiedBeds: 0,
    availableBeds: 0,
    maintenanceBeds: 0
  });

  const occupancyRate = floorStats.totalBeds > 0 
    ? (floorStats.occupiedBeds / floorStats.totalBeds) * 100 
    : 0;

  const getOccupancyColor = (rate: number) => {
    if (rate >= 90) return 'text-red-600 bg-red-100';
    if (rate >= 75) return 'text-orange-600 bg-orange-100';
    if (rate >= 50) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader 
        className="cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={onToggleExpansion}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              {isExpanded ? <ArrowDown className="h-4 w-4" /> : <ArrowUp className="h-4 w-4" />}
              <Home className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">{floor.floorName}</CardTitle>
            </div>
            
            {floor.description && (
              <span className="text-sm text-muted-foreground">
                {floor.description}
              </span>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {floorStats.totalRooms} rooms
            </Badge>
            <Badge variant="outline" className="text-xs">
              {floorStats.totalBeds} beds
            </Badge>
            <Badge 
              variant="outline" 
              className={`text-xs ${getOccupancyColor(occupancyRate)}`}
            >
              {occupancyRate.toFixed(0)}% occupied
            </Badge>
          </div>
        </div>

        {/* Floor Statistics Bar */}
        {!isCompact && (
          <div className="mt-3 space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Bed Status</span>
              <span className="text-muted-foreground">
                {floorStats.occupiedBeds}/{floorStats.totalBeds} occupied
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="flex h-2 rounded-full overflow-hidden">
                <div 
                  className="bg-blue-500" 
                  style={{ width: `${(floorStats.occupiedBeds / floorStats.totalBeds) * 100}%` }}
                />
                <div 
                  className="bg-green-500" 
                  style={{ width: `${(floorStats.availableBeds / floorStats.totalBeds) * 100}%` }}
                />
                <div 
                  className="bg-yellow-500" 
                  style={{ width: `${(floorStats.maintenanceBeds / floorStats.totalBeds) * 100}%` }}
                />
              </div>
            </div>
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full" />
                <span>Occupied ({floorStats.occupiedBeds})</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                <span>Available ({floorStats.availableBeds})</span>
              </div>
              {floorStats.maintenanceBeds > 0 && (
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                  <span>Maintenance ({floorStats.maintenanceBeds})</span>
                </div>
              )}
            </div>
          </div>
        )}
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          {rooms.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Home className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">No rooms found on this floor</p>
            </div>
          ) : (
            <div className="space-y-4">
              {isCompact ? (
                <CompactRoomLayout 
                  rooms={rooms} 
                  onBedClick={onBedClick}
                />
              ) : (
                <DetailedRoomLayout 
                  rooms={rooms} 
                  onBedClick={onBedClick}
                />
              )}
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};

// Detailed Room Layout Component
interface DetailedRoomLayoutProps {
  rooms: Room[];
  onBedClick?: (bed: Bed) => void;
}

const DetailedRoomLayout: React.FC<DetailedRoomLayoutProps> = ({
  rooms,
  onBedClick
}) => {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {rooms.map((room) => {
        const beds = getBedsByRoomId(room.id);
        return (
          <RoomLayout
            key={room.id}
            roomNumber={room.roomNumber}
            beds={beds}
            onBedClick={onBedClick}
          />
        );
      })}
    </div>
  );
};

// Compact Room Layout Component
interface CompactRoomLayoutProps {
  rooms: Room[];
  onBedClick?: (bed: Bed) => void;
}

const CompactRoomLayout: React.FC<CompactRoomLayoutProps> = ({
  rooms,
  onBedClick
}) => {
  return (
    <div className="space-y-3">
      {rooms.map((room) => {
        const beds = getBedsByRoomId(room.id);
        const occupancyStatus = getRoomOccupancyStatus(room.id);
        
        return (
          <Card key={room.id} className="border">
            <div className="p-3">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Home className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium text-sm">{room.roomNumber}</span>
                  <Badge variant="outline" className="text-xs capitalize">
                    {room.roomType}
                  </Badge>
                </div>
                <Badge 
                  variant="secondary" 
                  className="text-xs"
                >
                  {occupancyStatus.occupiedBeds}/{occupancyStatus.totalBeds}
                </Badge>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4">
                {beds.map((bed) => (
                  <DroppableBedZone
                    key={bed.id}
                    bed={bed}
                    onBedClick={onBedClick}
                    className="min-h-[160px] sm:min-h-[180px]"
                  />
                ))}
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};
