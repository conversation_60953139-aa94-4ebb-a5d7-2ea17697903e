import React from 'react';
import { 
  Bed as BedIcon, 
  User, 
  CheckCircle, 
  AlertTriangle, 
  Wrench, 
  Clock,
  Star,
  Zap,
  Wifi,
  Lock,
  Lightbulb
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Droppable, useDragDrop, isTenantCompatibleWithBed, getTenantBedCompatibilityScore } from './DragDropProvider';
import { Bed, HostelTenant, getHostelTenantById } from '@/data/mockData';

interface DroppableBedZoneProps {
  bed: Bed;
  className?: string;
  onBedClick?: (bed: Bed) => void;
  showCompatibilityIndicator?: boolean;
}

export const DroppableBedZone: React.FC<DroppableBedZoneProps> = ({
  bed,
  className = '',
  onBedClick,
  showCompatibilityIndicator = true
}) => {
  const { draggedItem, isDragging } = useDragDrop();
  
  // Get current tenant if bed is occupied
  const currentTenant = bed.currentTenantId ? getHostelTenantById(bed.currentTenantId) : null;
  
  // Check compatibility with dragged tenant
  const draggedTenant = draggedItem?.type === 'tenant' ? draggedItem.data : null;
  const isCompatible = draggedTenant ? isTenantCompatibleWithBed(draggedTenant, bed) : false;
  const compatibilityScore = draggedTenant ? getTenantBedCompatibilityScore(draggedTenant, bed) : 0;

  const getBedStatusColor = (status: Bed['status']) => {
    switch (status) {
      case 'available':
        return 'border-green-200 bg-green-50 hover:bg-green-100';
      case 'occupied':
        return 'border-blue-200 bg-blue-50';
      case 'maintenance':
        return 'border-yellow-200 bg-yellow-50';
      case 'reserved':
        return 'border-purple-200 bg-purple-50';
      case 'inactive':
        return 'border-gray-200 bg-gray-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getBedStatusIcon = (status: Bed['status']) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'occupied':
        return <User className="h-4 w-4 text-blue-600" />;
      case 'maintenance':
        return <Wrench className="h-4 w-4 text-yellow-600" />;
      case 'reserved':
        return <Clock className="h-4 w-4 text-purple-600" />;
      case 'inactive':
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getBedTypeIcon = (bedType: string) => {
    switch (bedType) {
      case 'single':
        return <BedIcon className="h-3 w-3" />;
      case 'bunk_top':
        return <div className="flex flex-col"><BedIcon className="h-2 w-2" /><BedIcon className="h-2 w-2" /></div>;
      case 'bunk_bottom':
        return <div className="flex flex-col"><BedIcon className="h-2 w-2" /><BedIcon className="h-2 w-2" /></div>;
      default:
        return <BedIcon className="h-3 w-3" />;
    }
  };

  const getCompatibilityIndicator = () => {
    if (!isDragging || !draggedTenant || !showCompatibilityIndicator) return null;

    if (!isCompatible) {
      return (
        <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
          <AlertTriangle className="h-3 w-3 text-white" />
        </div>
      );
    }

    const getScoreColor = (score: number) => {
      if (score >= 80) return 'bg-green-500';
      if (score >= 60) return 'bg-yellow-500';
      return 'bg-orange-500';
    };

    return (
      <div className={`absolute -top-2 -right-2 w-6 h-6 ${getScoreColor(compatibilityScore)} rounded-full flex items-center justify-center`}>
        <span className="text-xs font-bold text-white">{compatibilityScore}</span>
      </div>
    );
  };

  const isValidDrop = (item: any) => {
    if (item.type !== 'tenant') return false;
    return isTenantCompatibleWithBed(item.data, bed);
  };

  const handleClick = () => {
    if (onBedClick && !isDragging) {
      onBedClick(bed);
    }
  };

  return (
    <Droppable
      id={bed.id}
      isValidDrop={isValidDrop}
      disabled={bed.status !== 'available'}
      className={`relative ${className}`}
    >
      <Card
        data-bed-id={bed.id}
        className={`
          relative cursor-pointer border-2 transition-all duration-300 transform-gpu
          ${getBedStatusColor(bed.status)}
          ${bed.status === 'available' ? 'hover:-translate-y-1 hover:shadow-md' : ''}
          ${bed.status === 'occupied' ? 'opacity-90' : ''}
          ${bed.status === 'maintenance' ? 'opacity-80 grayscale-[0.2]' : ''}
          ${isDragging && isCompatible ? 'ring-2 ring-green-400 ring-opacity-60 shadow-green-200 animate-pulse' : ''}
          ${isDragging && !isCompatible && bed.status === 'available' ? 'ring-2 ring-red-400 ring-opacity-60 shadow-red-200 animate-pulse' : ''}
          ${bed.status !== 'available' ? 'cursor-not-allowed opacity-75' : ''}
        `}
        onClick={handleClick}
      >
        <CardContent className="p-2 sm:p-3">
          {/* Bed Header */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-1 sm:gap-2 min-w-0 flex-1">
              <div className="flex items-center gap-1">
                <div className="hidden sm:block">
                  {getBedTypeIcon(bed.bedType)}
                </div>
                <span className="font-semibold text-xs sm:text-sm truncate">{bed.bedNumber}</span>
              </div>
              <div className="flex-shrink-0">
                {getBedStatusIcon(bed.status)}
              </div>
            </div>

            <Badge
              variant="outline"
              className="text-xs capitalize ml-1 hidden sm:inline-flex"
            >
              {bed.status}
            </Badge>
          </div>

          {/* Bed Type and Price */}
          <div className="space-y-1 mb-2 sm:mb-3">
            <div className="text-xs text-muted-foreground capitalize truncate">
              {bed.bedType.replace('_', ' ')} • {bed.bedSize}
            </div>
            <div className="text-xs sm:text-sm font-medium">
              ₹{bed.pricePerMonth.toLocaleString()}/month
            </div>
          </div>

          {/* Current Tenant (if occupied) */}
          {currentTenant && bed.status === 'occupied' && (
            <div className="space-y-1 sm:space-y-2 p-2 bg-white rounded-md border">
              <div className="flex items-center gap-2">
                <Avatar className="h-5 w-5 sm:h-6 sm:w-6">
                  <AvatarFallback className="text-xs bg-blue-100 text-blue-700">
                    {currentTenant.firstName.charAt(0)}{currentTenant.lastName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="text-xs font-medium truncate">
                    {currentTenant.firstName} {currentTenant.lastName}
                  </div>
                  <div className="text-xs text-muted-foreground truncate">
                    {currentTenant.phone}
                  </div>
                </div>
              </div>
              {bed.checkInDate && (
                <div className="text-xs text-muted-foreground">
                  Since: {new Date(bed.checkInDate).toLocaleDateString()}
                </div>
              )}
            </div>
          )}

          {/* Maintenance Info */}
          {bed.status === 'maintenance' && bed.maintenanceNotes && (
            <div className="p-2 bg-yellow-50 rounded-md border border-yellow-200">
              <div className="text-xs font-medium text-yellow-800 mb-1">Maintenance Required</div>
              <div className="text-xs text-yellow-700 truncate sm:whitespace-normal">{bed.maintenanceNotes}</div>
            </div>
          )}

          {/* Bed Amenities */}
          <div className="flex items-center gap-1 mt-2 pt-2 border-t border-gray-100">
            {bed.hasStorage && (
              <div className="w-4 h-4 bg-blue-100 rounded flex items-center justify-center" title="Storage">
                <Lock className="h-2 w-2 text-blue-600" />
              </div>
            )}
            {bed.hasPrivacyCurtain && (
              <div className="w-4 h-4 bg-purple-100 rounded flex items-center justify-center" title="Privacy Curtain">
                <div className="w-2 h-2 bg-purple-600 rounded"></div>
              </div>
            )}
            {bed.hasReadingLight && (
              <div className="w-4 h-4 bg-yellow-100 rounded flex items-center justify-center" title="Reading Light">
                <Lightbulb className="h-2 w-2 text-yellow-600" />
              </div>
            )}
            {bed.hasPowerOutlet && (
              <div className="w-4 h-4 bg-green-100 rounded flex items-center justify-center" title="Power Outlet">
                <Zap className="h-2 w-2 text-green-600" />
              </div>
            )}
          </div>

          {/* Drag Compatibility Indicator */}
          {getCompatibilityIndicator()}

          {/* Drop Zone Indicator */}
          {isDragging && bed.status === 'available' && (
            <div className={`
              absolute inset-0 rounded-lg border-2 border-dashed flex items-center justify-center
              ${isCompatible ? 'border-green-400 bg-green-50 bg-opacity-50' : 'border-red-400 bg-red-50 bg-opacity-50'}
            `}>
              <div className={`
                text-xs font-medium px-2 py-1 rounded
                ${isCompatible ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}
              `}>
                {isCompatible ? `Drop Here (${compatibilityScore}% match)` : 'Not Compatible'}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </Droppable>
  );
};

// Bed Grid Component for organizing beds in a room layout
interface BedGridProps {
  beds: Bed[];
  className?: string;
  onBedClick?: (bed: Bed) => void;
  columns?: number;
}

export const BedGrid: React.FC<BedGridProps> = ({
  beds,
  className = '',
  onBedClick,
  columns = 2
}) => {
  // Determine responsive grid classes based on bed count and screen size
  const getGridClasses = () => {
    const bedCount = beds.length;

    if (bedCount === 1) {
      return 'grid-cols-1';
    } else if (bedCount === 2) {
      return 'grid-cols-1 sm:grid-cols-2';
    } else if (bedCount <= 4) {
      return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2';
    } else if (bedCount <= 6) {
      return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3';
    } else {
      return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4';
    }
  };

  return (
    <div className={`grid gap-2 sm:gap-3 ${getGridClasses()} ${className}`}>
      {beds.map((bed) => (
        <DroppableBedZone
          key={bed.id}
          bed={bed}
          onBedClick={onBedClick}
        />
      ))}
    </div>
  );
};

// Room Layout Component
interface RoomLayoutProps {
  roomNumber: string;
  beds: Bed[];
  className?: string;
  onBedClick?: (bed: Bed) => void;
}

export const RoomLayout: React.FC<RoomLayoutProps> = ({
  roomNumber,
  beds,
  className = '',
  onBedClick
}) => {
  const occupiedBeds = beds.filter(bed => bed.status === 'occupied').length;
  const totalBeds = beds.length;
  const occupancyRate = totalBeds > 0 ? (occupiedBeds / totalBeds) * 100 : 0;

  const getOccupancyColor = (rate: number) => {
    if (rate === 100) return 'text-red-600 bg-red-100';
    if (rate >= 75) return 'text-orange-600 bg-orange-100';
    if (rate >= 50) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  return (
    <Card className={`${className}`}>
      <div className="p-4 border-b bg-gray-50">
        <div className="flex items-center justify-between">
          <h4 className="font-semibold text-sm">{roomNumber}</h4>
          <div className="flex items-center gap-2">
            <Badge 
              variant="outline" 
              className={`text-xs ${getOccupancyColor(occupancyRate)}`}
            >
              {occupiedBeds}/{totalBeds} beds
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {occupancyRate.toFixed(0)}%
            </Badge>
          </div>
        </div>
      </div>
      
      <div className="p-3 sm:p-4">
        <BedGrid
          beds={beds}
          onBedClick={onBedClick}
        />
      </div>
    </Card>
  );
};
