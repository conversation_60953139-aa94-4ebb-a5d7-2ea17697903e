import React from 'react';
import { 
  Bed as BedIcon, 
  User, 
  CheckCircle, 
  AlertTriangle, 
  Wrench, 
  Clock,
  Star,
  Zap,
  Wifi,
  Lock,
  Lightbulb
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Droppable, useDragDrop, isTenantCompatibleWithBed, getTenantBedCompatibilityScore } from './DragDropProvider';
import { Bed, HostelTenant, getHostelTenantById } from '@/data/mockData';

interface DroppableBedZoneProps {
  bed: Bed;
  className?: string;
  onBedClick?: (bed: Bed) => void;
  showCompatibilityIndicator?: boolean;
}

export const DroppableBedZone: React.FC<DroppableBedZoneProps> = ({
  bed,
  className = '',
  onBedClick,
  showCompatibilityIndicator = true
}) => {
  const { draggedItem, isDragging } = useDragDrop();
  
  // Get current tenant if bed is occupied
  const currentTenant = bed.currentTenantId ? getHostelTenantById(bed.currentTenantId) : null;
  
  // Check compatibility with dragged tenant
  const draggedTenant = draggedItem?.type === 'tenant' ? draggedItem.data : null;
  const isCompatible = draggedTenant ? isTenantCompatibleWithBed(draggedTenant, bed) : false;
  const compatibilityScore = draggedTenant ? getTenantBedCompatibilityScore(draggedTenant, bed) : 0;

  const getBedStatusColor = (status: Bed['status']) => {
    switch (status) {
      case 'available':
        return 'border-green-200 bg-green-50 hover:bg-green-100';
      case 'occupied':
        return 'border-blue-200 bg-blue-50';
      case 'maintenance':
        return 'border-yellow-200 bg-yellow-50';
      case 'reserved':
        return 'border-purple-200 bg-purple-50';
      case 'inactive':
        return 'border-gray-200 bg-gray-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getBedStatusIcon = (status: Bed['status']) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'occupied':
        return <User className="h-4 w-4 text-blue-600" />;
      case 'maintenance':
        return <Wrench className="h-4 w-4 text-yellow-600" />;
      case 'reserved':
        return <Clock className="h-4 w-4 text-purple-600" />;
      case 'inactive':
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getBedTypeIcon = (bedType: string) => {
    switch (bedType) {
      case 'single':
        return <BedIcon className="h-3 w-3" />;
      case 'bunk_top':
        return <div className="flex flex-col"><BedIcon className="h-2 w-2" /><BedIcon className="h-2 w-2" /></div>;
      case 'bunk_bottom':
        return <div className="flex flex-col"><BedIcon className="h-2 w-2" /><BedIcon className="h-2 w-2" /></div>;
      default:
        return <BedIcon className="h-3 w-3" />;
    }
  };

  const getCompatibilityIndicator = () => {
    if (!isDragging || !draggedTenant || !showCompatibilityIndicator) return null;

    if (!isCompatible) {
      return (
        <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
          <AlertTriangle className="h-3 w-3 text-white" />
        </div>
      );
    }

    const getScoreColor = (score: number) => {
      if (score >= 80) return 'bg-green-500';
      if (score >= 60) return 'bg-yellow-500';
      return 'bg-orange-500';
    };

    return (
      <div className={`absolute -top-2 -right-2 w-6 h-6 ${getScoreColor(compatibilityScore)} rounded-full flex items-center justify-center`}>
        <span className="text-xs font-bold text-white">{compatibilityScore}</span>
      </div>
    );
  };

  const isValidDrop = (item: any) => {
    if (item.type !== 'tenant') return false;
    return isTenantCompatibleWithBed(item.data, bed);
  };

  const handleClick = () => {
    if (onBedClick && !isDragging) {
      onBedClick(bed);
    }
  };

  return (
    <Droppable
      id={bed.id}
      isValidDrop={isValidDrop}
      disabled={bed.status !== 'available'}
      className={`relative ${className}`}
    >
      <Card
        data-bed-id={bed.id}
        className={`
          relative cursor-pointer border-2 transition-all duration-300 transform-gpu h-full min-h-[200px] sm:min-h-[220px]
          ${getBedStatusColor(bed.status)}
          ${bed.status === 'available' ? 'hover:-translate-y-1 hover:shadow-lg' : ''}
          ${bed.status === 'occupied' ? 'opacity-90' : ''}
          ${bed.status === 'maintenance' ? 'opacity-80 grayscale-[0.2]' : ''}
          ${isDragging && isCompatible ? 'ring-2 ring-green-400 ring-opacity-60 shadow-green-200 animate-pulse' : ''}
          ${isDragging && !isCompatible && bed.status === 'available' ? 'ring-2 ring-red-400 ring-opacity-60 shadow-red-200 animate-pulse' : ''}
          ${bed.status !== 'available' ? 'cursor-not-allowed opacity-75' : ''}
        `}
        onClick={handleClick}
      >
        <CardContent className="p-4 sm:p-5 h-full flex flex-col">
          {/* Bed Header */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <div className="hidden sm:block">
                  {getBedTypeIcon(bed.bedType)}
                </div>
                <span className="font-bold text-base sm:text-lg">{bed.bedNumber}</span>
              </div>
              <Badge
                variant="outline"
                className="text-xs font-medium capitalize px-2 py-1"
              >
                {bed.status}
              </Badge>
            </div>

            <div className="flex-shrink-0">
              {getBedStatusIcon(bed.status)}
            </div>
          </div>

          {/* Bed Type and Price */}
          <div className="mb-4 pb-3 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground capitalize font-medium">
                {bed.bedType.replace('_', ' ')} • {bed.bedSize}
              </p>
              <p className="text-sm font-bold text-primary">
                ₹{bed.pricePerMonth.toLocaleString()}/mo
              </p>
            </div>
          </div>

          {/* Current Tenant (if occupied) */}
          {currentTenant && bed.status === 'occupied' && (
            <div className="flex-grow">
              <div className="bg-white rounded-lg border p-4 space-y-3">
                {/* Tenant Profile */}
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                    <span className="text-sm font-bold text-blue-700">
                      {currentTenant.firstName.charAt(0)}{currentTenant.lastName.charAt(0)}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-semibold text-sm leading-tight">
                      {currentTenant.firstName} {currentTenant.lastName}
                    </p>
                    <p className="text-xs text-muted-foreground capitalize">
                      {currentTenant.occupation?.replace('_', ' ') || 'Tenant'}
                    </p>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded bg-gray-100 flex items-center justify-center">
                      <span className="text-xs">📱</span>
                    </div>
                    <p className="text-xs font-medium">
                      {currentTenant.phone}
                    </p>
                  </div>

                  {bed.checkInDate && (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded bg-gray-100 flex items-center justify-center">
                        <span className="text-xs">📅</span>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Since: {new Date(bed.checkInDate).toLocaleDateString('en-IN', {
                          day: '2-digit',
                          month: 'short',
                          year: 'numeric'
                        })}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Maintenance Info */}
          {bed.status === 'maintenance' && bed.maintenanceNotes && (
            <div className="flex-grow">
              <div className="bg-yellow-50 rounded-lg border border-yellow-200 p-4 space-y-2">
                <div className="flex items-center gap-2">
                  <div className="w-5 h-5 rounded bg-yellow-200 flex items-center justify-center">
                    <span className="text-xs">🔧</span>
                  </div>
                  <p className="text-sm font-semibold text-yellow-800">Maintenance Required</p>
                </div>
                <p className="text-xs text-yellow-700 leading-relaxed pl-7">
                  {bed.maintenanceNotes}
                </p>
              </div>
            </div>
          )}

          {/* Available Bed Placeholder */}
          {bed.status === 'available' && (
            <div className="flex-grow flex items-center justify-center">
              <div className="text-center py-4">
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-2">
                  <span className="text-lg">✅</span>
                </div>
                <p className="text-sm font-medium text-green-700">Available</p>
                <p className="text-xs text-muted-foreground">Ready for allocation</p>
              </div>
            </div>
          )}

          {/* Bed Amenities */}
          <div className="flex items-center gap-1 mt-2 pt-2 border-t border-gray-100">
            {bed.hasStorage && (
              <div className="w-4 h-4 bg-blue-100 rounded flex items-center justify-center" title="Storage">
                <Lock className="h-2 w-2 text-blue-600" />
              </div>
            )}
            {bed.hasPrivacyCurtain && (
              <div className="w-4 h-4 bg-purple-100 rounded flex items-center justify-center" title="Privacy Curtain">
                <div className="w-2 h-2 bg-purple-600 rounded"></div>
              </div>
            )}
            {bed.hasReadingLight && (
              <div className="w-4 h-4 bg-yellow-100 rounded flex items-center justify-center" title="Reading Light">
                <Lightbulb className="h-2 w-2 text-yellow-600" />
              </div>
            )}
            {bed.hasPowerOutlet && (
              <div className="w-4 h-4 bg-green-100 rounded flex items-center justify-center" title="Power Outlet">
                <Zap className="h-2 w-2 text-green-600" />
              </div>
            )}
          </div>

          {/* Drag Compatibility Indicator */}
          {getCompatibilityIndicator()}

          {/* Drop Zone Indicator */}
          {isDragging && bed.status === 'available' && (
            <div className={`
              absolute inset-0 rounded-lg border-2 border-dashed flex items-center justify-center
              ${isCompatible ? 'border-green-400 bg-green-50 bg-opacity-50' : 'border-red-400 bg-red-50 bg-opacity-50'}
            `}>
              <div className={`
                text-xs font-medium px-2 py-1 rounded
                ${isCompatible ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}
              `}>
                {isCompatible ? `Drop Here (${compatibilityScore}% match)` : 'Not Compatible'}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </Droppable>
  );
};

// Bed Grid Component for organizing beds in a room layout
interface BedGridProps {
  beds: Bed[];
  className?: string;
  onBedClick?: (bed: Bed) => void;
  columns?: number;
}

export const BedGrid: React.FC<BedGridProps> = ({
  beds,
  className = '',
  onBedClick,
  columns = 2
}) => {
  // Determine responsive grid classes based on bed count and screen size
  const getGridClasses = () => {
    const bedCount = beds.length;

    if (bedCount === 1) {
      return 'grid-cols-1';
    } else if (bedCount === 2) {
      return 'grid-cols-1 sm:grid-cols-2';
    } else if (bedCount <= 4) {
      return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2';
    } else if (bedCount <= 6) {
      return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3';
    } else {
      return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4';
    }
  };

  return (
    <div className={`grid gap-4 sm:gap-6 ${getGridClasses()} ${className}`}>
      {beds.map((bed) => (
        <DroppableBedZone
          key={bed.id}
          bed={bed}
          onBedClick={onBedClick}
          className="min-h-[200px] sm:min-h-[220px]"
        />
      ))}
    </div>
  );
};

// Room Layout Component
interface RoomLayoutProps {
  roomNumber: string;
  beds: Bed[];
  className?: string;
  onBedClick?: (bed: Bed) => void;
}

export const RoomLayout: React.FC<RoomLayoutProps> = ({
  roomNumber,
  beds,
  className = '',
  onBedClick
}) => {
  const occupiedBeds = beds.filter(bed => bed.status === 'occupied').length;
  const totalBeds = beds.length;
  const occupancyRate = totalBeds > 0 ? (occupiedBeds / totalBeds) * 100 : 0;

  const getOccupancyColor = (rate: number) => {
    if (rate === 100) return 'text-red-600 bg-red-100';
    if (rate >= 75) return 'text-orange-600 bg-orange-100';
    if (rate >= 50) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  return (
    <Card className={`border-2 ${className}`}>
      <div className="p-4 sm:p-5 border-b bg-gray-50">
        {/* Room Title and Status */}
        <div className="flex items-start justify-between gap-3 mb-3">
          <div className="flex-1 min-w-0">
            <h4 className="font-bold text-lg sm:text-xl mb-1">{roomNumber}</h4>
          </div>
          <div className="flex flex-col gap-2 items-end">
            <Badge variant="outline" className="text-xs font-medium">
              {occupiedBeds}/{totalBeds} beds
            </Badge>
            <Badge
              variant="outline"
              className={`text-xs font-medium ${
                occupancyRate === 100
                  ? 'bg-red-100 text-red-800 border-red-200'
                  : occupancyRate > 50
                  ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                  : 'bg-green-100 text-green-800 border-green-200'
              }`}
            >
              {occupancyRate.toFixed(0)}%
            </Badge>
          </div>
        </div>

        {/* Occupancy Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Occupancy Status</span>
            <span>{occupiedBeds} of {totalBeds} occupied</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all ${
                occupancyRate === 100
                  ? 'bg-red-500'
                  : occupancyRate > 50
                  ? 'bg-yellow-500'
                  : 'bg-green-500'
              }`}
              style={{ width: `${occupancyRate}%` }}
            />
          </div>
        </div>
      </div>

      <div className="p-4 sm:p-6">
        <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {beds.map((bed) => (
            <DroppableBedZone
              key={bed.id}
              bed={bed}
              onBedClick={onBedClick}
              className="min-h-[200px] sm:min-h-[220px]"
            />
          ))}
        </div>
      </div>
    </Card>
  );
};
