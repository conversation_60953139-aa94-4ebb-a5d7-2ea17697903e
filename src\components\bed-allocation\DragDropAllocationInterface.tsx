import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Bed as BedIcon, 
  Search, 
  Filter,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DragDropProvider, useDragDrop } from './DragDropProvider';
import { TenantCardsPanel } from './DraggableTenantCard';
import { DragDropFloorPlan } from './DragDropFloorPlan';
import { DragDropConfirmationDialog } from './DragDropConfirmationDialog';
import { 
  Bed, 
  HostelTenant, 
  mockHostelTenants,
  mockBeds,
  getBedsByHostelId,
  getTenantsByHostelId,
  getHostelTenantById
} from '@/data/mockData';
import { useToast } from '@/hooks/use-toast';

interface DragDropAllocationInterfaceProps {
  hostelId: string;
  onAllocationRequest: (bedId: string, tenant: HostelTenant) => void;
  onBedClick?: (bed: Bed) => void;
  className?: string;
}

export const DragDropAllocationInterface: React.FC<DragDropAllocationInterfaceProps> = ({
  hostelId,
  onAllocationRequest,
  onBedClick,
  className = ''
}) => {
  return (
    <DragDropProvider>
      <DragDropAllocationContent
        hostelId={hostelId}
        onAllocationRequest={onAllocationRequest}
        onBedClick={onBedClick}
        className={className}
      />
    </DragDropProvider>
  );
};

// Main Content Component (inside DragDropProvider)
const DragDropAllocationContent: React.FC<DragDropAllocationInterfaceProps> = ({
  hostelId,
  onAllocationRequest,
  onBedClick,
  className = ''
}) => {
  const { toast } = useToast();
  const { setOnDropCallback, isDragging, draggedItem } = useDragDrop();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [tenantFilter, setTenantFilter] = useState<'all' | 'available' | 'allocated'>('available');
  const [budgetFilter, setBudgetFilter] = useState<'all' | 'low' | 'medium' | 'high'>('all');
  const [showInstructions, setShowInstructions] = useState(true);

  // Confirmation dialog state
  const [isConfirmationOpen, setIsConfirmationOpen] = useState(false);
  const [pendingAllocation, setPendingAllocation] = useState<{
    bed: Bed;
    tenant: HostelTenant;
  } | null>(null);

  // Get data for the hostel
  const hostelBeds = getBedsByHostelId(hostelId);
  const allTenants = mockHostelTenants; // In a real app, this would be filtered by hostel or region

  // Debug logging
  console.log('DragDropAllocationInterface data:', {
    hostelId,
    hostelBedsCount: hostelBeds.length,
    allTenantsCount: allTenants.length,
    firstTenant: allTenants[0],
    availableTenants: allTenants.filter(t => !t.currentBedId).length
  });
  
  // Filter tenants based on search and filters
  const filteredTenants = allTenants.filter(tenant => {
    // Search filter
    const matchesSearch = tenant.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.phone.includes(searchTerm);

    // Availability filter
    const matchesAvailability = tenantFilter === 'all' || 
                               (tenantFilter === 'available' && !tenant.currentBedId) ||
                               (tenantFilter === 'allocated' && tenant.currentBedId);

    // Budget filter
    const matchesBudget = budgetFilter === 'all' ||
                         (budgetFilter === 'low' && tenant.budgetRange.max <= 8000) ||
                         (budgetFilter === 'medium' && tenant.budgetRange.max > 8000 && tenant.budgetRange.max <= 15000) ||
                         (budgetFilter === 'high' && tenant.budgetRange.max > 15000);

    return matchesSearch && matchesAvailability && matchesBudget;
  });

  // Statistics
  const availableBeds = hostelBeds.filter(bed => bed.status === 'available').length;
  const availableTenants = filteredTenants.filter(tenant => !tenant.currentBedId).length;

  // Handle drop events
  useEffect(() => {
    const handleDrop = (bedId: string, tenant: HostelTenant) => {
      const bed = hostelBeds.find(b => b.id === bedId);
      if (!bed) {
        toast({
          title: 'Error',
          description: 'Bed not found',
          variant: 'destructive',
        });
        return;
      }

      if (bed.status !== 'available') {
        toast({
          title: 'Cannot Allocate',
          description: 'This bed is not available for allocation',
          variant: 'destructive',
        });
        return;
      }

      if (tenant.currentBedId) {
        toast({
          title: 'Cannot Allocate',
          description: 'This tenant is already allocated to another bed',
          variant: 'destructive',
        });
        return;
      }

      // Check budget compatibility
      if (bed.pricePerMonth > tenant.budgetRange.max || bed.pricePerMonth < tenant.budgetRange.min) {
        toast({
          title: 'Budget Mismatch',
          description: `Bed price (₹${bed.pricePerMonth.toLocaleString()}) is outside tenant's budget range (₹${tenant.budgetRange.min.toLocaleString()} - ₹${tenant.budgetRange.max.toLocaleString()})`,
          variant: 'destructive',
        });
        return;
      }

      // Success - show confirmation dialog
      setPendingAllocation({ bed, tenant });
      setIsConfirmationOpen(true);
    };

    setOnDropCallback(handleDrop);
  }, [hostelBeds, setOnDropCallback, onAllocationRequest, toast]);

  // Handle confirmation dialog
  const handleConfirmAllocation = async (allocationData: any) => {
    if (!pendingAllocation) return;

    try {
      await onAllocationRequest(pendingAllocation.bed.id, pendingAllocation.tenant);

      // Add success animation class to the bed element
      const bedElement = document.querySelector(`[data-bed-id="${pendingAllocation.bed.id}"]`);
      if (bedElement) {
        bedElement.classList.add('allocation-success');
        setTimeout(() => {
          bedElement.classList.remove('allocation-success');
        }, 600);
      }

      toast({
        title: '🎉 Allocation Confirmed',
        description: `${pendingAllocation.tenant.firstName} ${pendingAllocation.tenant.lastName} has been allocated to bed ${pendingAllocation.bed.bedNumber}`,
      });
    } catch (error) {
      toast({
        title: 'Allocation Failed',
        description: 'Failed to process the allocation. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsConfirmationOpen(false);
      setPendingAllocation(null);
    }
  };

  const handleCancelAllocation = () => {
    setIsConfirmationOpen(false);
    setPendingAllocation(null);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setTenantFilter('available');
    setBudgetFilter('all');
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Instructions */}
      {showInstructions && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>
              Drag tenant cards from the sidebar onto available beds to create allocations. 
              Green indicators show compatible matches, red indicates incompatibility.
            </span>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setShowInstructions(false)}
            >
              Got it
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Statistics Bar */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BedIcon className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Available Beds</p>
                <p className="text-2xl font-bold text-green-600">{availableBeds}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Available Tenants</p>
                <p className="text-2xl font-bold text-blue-600">{availableTenants}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Possible Matches</p>
                <p className="text-2xl font-bold text-purple-600">
                  {Math.min(availableBeds, availableTenants)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className={`h-5 w-5 rounded-full ${isDragging ? 'bg-orange-500' : 'bg-gray-400'}`} />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Drag Status</p>
                <p className="text-sm font-bold">
                  {isDragging ? 'Dragging...' : 'Ready'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Tenant Sidebar */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Tenants
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search tenants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filters */}
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium mb-2 block">Availability</label>
                  <Select value={tenantFilter} onValueChange={(value: any) => setTenantFilter(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Tenants</SelectItem>
                      <SelectItem value="available">Available</SelectItem>
                      <SelectItem value="allocated">Already Allocated</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Budget Range</label>
                  <Select value={budgetFilter} onValueChange={(value: any) => setBudgetFilter(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Budgets</SelectItem>
                      <SelectItem value="low">Low (≤₹8,000)</SelectItem>
                      <SelectItem value="medium">Medium (₹8,001-₹15,000)</SelectItem>
                      <SelectItem value="high">High (>₹15,000)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={clearFilters}
                  className="w-full"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Tenant Cards */}
          <TenantCardsPanel
            tenants={filteredTenants}
            title={`${filteredTenants.length} Tenants`}
            showCompatibility={isDragging}
            compatibilityBeds={hostelBeds.filter(bed => bed.status === 'available')}
          />
        </div>

        {/* Floor Plan */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <BedIcon className="h-5 w-5" />
                  Floor Plan
                </CardTitle>
                
                {isDragging && draggedItem && draggedItem.data && (
                  <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                    Dragging: {draggedItem.data.firstName} {draggedItem.data.lastName}
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <DragDropFloorPlan
                hostelId={hostelId}
                onBedClick={onBedClick}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Drag Status Overlay */}
      {isDragging && (
        <div className="fixed bottom-4 right-4 z-50">
          <Card className="shadow-lg border-orange-200 bg-orange-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-orange-700">
                <div className="animate-pulse w-2 h-2 bg-orange-500 rounded-full" />
                <span className="text-sm font-medium">
                  Drop on an available bed to allocate
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Confirmation Dialog */}
      <DragDropConfirmationDialog
        isOpen={isConfirmationOpen}
        onClose={handleCancelAllocation}
        onConfirm={handleConfirmAllocation}
        bed={pendingAllocation?.bed || null}
        tenant={pendingAllocation?.tenant || null}
      />
    </div>
  );
};
