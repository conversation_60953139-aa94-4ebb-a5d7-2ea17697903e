import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Building2,
  Bed as BedIcon,
  Users,
  Home,
  Filter,
  Search,
  Calendar,
  User,
  Phone,
  Mail,
  MapPin,
  Clock,
  CheckCircle,
  AlertTriangle,
  Wrench,
  Eye,
  UserPlus,
  UserMinus
} from 'lucide-react';
import { BedAllocationModal } from '@/components/bed-allocation/BedAllocationModal';
import { CheckOutModal } from '@/components/bed-allocation/CheckOutModal';
import { MaintenanceModal } from '@/components/bed-allocation/MaintenanceModal';
import { AllocationHistory } from '@/components/bed-allocation/AllocationHistory';
import { DragDropAllocationInterface } from '@/components/bed-allocation/DragDropAllocationInterface';
import { BedAllocationService } from '@/services/bedAllocationService';
import { useBedAllocationUpdates, checkUpcomingEvents } from '@/hooks/useBedAllocationUpdates';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import {
  mockHostels,
  mockFloors,
  mockRooms,
  mockBeds,
  mockHostelTenants,
  mockBedAllocations,
  getFloorsByHostelId,
  getRoomsByHostelId,
  getBedsByRoomId,
  getHostelTenantById,
  getAllocationByBedId,
  getHostelOccupancySummary,
  getRoomOccupancyStatus,
  Bed,
  Room,
  Floor,
  HostelTenant,
  BedAllocation
} from '@/data/mockData';

export const BedAllocationDashboard: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  
  const [selectedHostelId, setSelectedHostelId] = useState<string>(searchParams.get('hostelId') || '1');
  const [selectedFloorId, setSelectedFloorId] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'available' | 'occupied' | 'maintenance' | 'reserved'>('all');
  const [selectedBed, setSelectedBed] = useState<Bed | null>(null);
  const [selectedTenant, setSelectedTenant] = useState<HostelTenant | null>(null);

  // Modal states
  const [isAllocationModalOpen, setIsAllocationModalOpen] = useState(false);
  const [isCheckOutModalOpen, setIsCheckOutModalOpen] = useState(false);
  const [isMaintenanceModalOpen, setIsMaintenanceModalOpen] = useState(false);
  const [modalBed, setModalBed] = useState<Bed | null>(null);

  // Get owner's approved hostels
  const ownerHostels = mockHostels.filter(hostel => 
    hostel.ownerId === user?.id && hostel.approvalStatus === 'approved'
  );

  // Get data for selected hostel
  const selectedHostel = ownerHostels.find(h => h.id === selectedHostelId);
  const hostelFloors = selectedHostelId ? getFloorsByHostelId(selectedHostelId) : [];
  const hostelRooms = selectedHostelId ? getRoomsByHostelId(selectedHostelId) : [];
  const hostelBeds = selectedHostelId ? mockBeds.filter(bed => bed.hostelId === selectedHostelId) : [];

  // Filter rooms and beds based on selected floor
  const filteredRooms = selectedFloorId === 'all' 
    ? hostelRooms 
    : hostelRooms.filter(room => room.floorId === selectedFloorId);

  // Filter beds based on search and status
  const filteredBeds = hostelBeds.filter(bed => {
    const room = hostelRooms.find(r => r.id === bed.roomId);
    const matchesFloor = selectedFloorId === 'all' || room?.floorId === selectedFloorId;
    const matchesSearch = bed.bedNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         room?.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) || '';
    const matchesStatus = statusFilter === 'all' || bed.status === statusFilter;
    return matchesFloor && matchesSearch && matchesStatus;
  });

  // Get occupancy summary for selected hostel
  const occupancySummary = selectedHostelId ? getHostelOccupancySummary(selectedHostelId) : null;

  // Real-time updates hook
  const {
    beds: realtimeBeds,
    allocations: realtimeAllocations,
    occupancySummary: realtimeOccupancySummary,
    upcomingEvents: realtimeUpcomingEvents,
    isLoading: isUpdating,
    refreshData
  } = useBedAllocationUpdates(selectedHostelId);

  useEffect(() => {
    if (ownerHostels.length > 0 && !selectedHostelId) {
      setSelectedHostelId(ownerHostels[0].id);
    }
  }, [ownerHostels, selectedHostelId]);

  // Check for upcoming events and create notifications
  useEffect(() => {
    if (selectedHostelId && user?.id) {
      checkUpcomingEvents(selectedHostelId, user.id);
    }
  }, [selectedHostelId, user?.id]);

  const getBedStatusColor = (status: Bed['status']) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'occupied':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'reserved':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getBedStatusIcon = (status: Bed['status']) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-3 w-3" />;
      case 'occupied':
        return <User className="h-3 w-3" />;
      case 'maintenance':
        return <Wrench className="h-3 w-3" />;
      case 'reserved':
        return <Clock className="h-3 w-3" />;
      case 'inactive':
        return <AlertTriangle className="h-3 w-3" />;
      default:
        return <AlertTriangle className="h-3 w-3" />;
    }
  };

  const handleBedClick = (bed: Bed) => {
    setSelectedBed(bed);
    if (bed.currentTenantId) {
      const tenant = getHostelTenantById(bed.currentTenantId);
      setSelectedTenant(tenant || null);
    } else {
      setSelectedTenant(null);
    }
  };

  // Modal handlers
  const handleAllocateBed = (bed: Bed) => {
    setModalBed(bed);
    setIsAllocationModalOpen(true);
  };

  const handleCheckOut = (bed: Bed) => {
    setModalBed(bed);
    setIsCheckOutModalOpen(true);
  };

  const handleMaintenance = (bed: Bed) => {
    setModalBed(bed);
    setIsMaintenanceModalOpen(true);
  };

  const handleBedAllocation = async (allocation: Partial<BedAllocation>) => {
    if (!modalBed || !user?.id) return;

    try {
      const allocationRequest = {
        bedId: modalBed.id,
        tenantId: allocation.tenantId!,
        checkInDate: allocation.checkInDate!,
        expectedCheckOutDate: allocation.expectedCheckOutDate!,
        monthlyRent: allocation.monthlyRent!,
        securityDeposit: allocation.securityDeposit!,
        advancePayment: allocation.advancePayment!,
        allocationNotes: allocation.allocationNotes
      };

      const result = await BedAllocationService.allocateBed(allocationRequest, user.id);

      if (result.success) {
        toast({
          title: 'Bed Allocated Successfully',
          description: result.message,
        });
        // Refresh data to show updated status
        await refreshData();
      } else {
        toast({
          title: 'Allocation Failed',
          description: result.error,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to allocate bed. Please try again.',
        variant: 'destructive',
      });
    }

    setIsAllocationModalOpen(false);
    setModalBed(null);
  };

  const handleBedCheckOut = async (checkOutData: any) => {
    if (!modalBed || !user?.id) return;

    try {
      const allocation = getAllocationByBedId(modalBed.id);
      if (!allocation) {
        toast({
          title: 'Error',
          description: 'No active allocation found for this bed.',
          variant: 'destructive',
        });
        return;
      }

      const result = await BedAllocationService.checkOutTenant(allocation.id, checkOutData, user.id);

      if (result.success) {
        toast({
          title: 'Check-out Completed',
          description: result.message,
        });
        // Refresh data to show updated status
        await refreshData();
      } else {
        toast({
          title: 'Check-out Failed',
          description: result.error,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to process check-out. Please try again.',
        variant: 'destructive',
      });
    }

    setIsCheckOutModalOpen(false);
    setModalBed(null);
  };

  const handleMaintenanceUpdate = async (maintenanceData: any) => {
    if (!modalBed || !user?.id) return;

    try {
      const result = await BedAllocationService.updateBedMaintenance(modalBed.id, maintenanceData, user.id);

      if (result.success) {
        toast({
          title: 'Maintenance Status Updated',
          description: result.message,
        });
        // Refresh data to show updated status
        await refreshData();
      } else {
        toast({
          title: 'Update Failed',
          description: result.error,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update maintenance status. Please try again.',
        variant: 'destructive',
      });
    }

    setIsMaintenanceModalOpen(false);
    setModalBed(null);
  };

  if (ownerHostels.length === 0) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Bed Allocation Dashboard</h1>
          <p className="text-muted-foreground">
            Manage bed allocations and room occupancy for your hostels
          </p>
        </div>
        
        <Card>
          <CardContent className="text-center py-12">
            <Building2 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Approved Hostels</h3>
            <p className="text-muted-foreground mb-6">
              You need to have approved hostels before you can manage bed allocations.
            </p>
            <Button onClick={() => window.location.href = '/owner/hostel-registration'}>
              <Building2 className="h-4 w-4 mr-2" />
              Register New Hostel
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Bed Allocation Dashboard</h1>
          <p className="text-muted-foreground">
            Manage bed allocations and room occupancy for your hostels
          </p>
        </div>
        {isUpdating && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            Updating...
          </div>
        )}
      </div>

      {/* Hostel Selection and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Select Hostel</label>
              <Select value={selectedHostelId} onValueChange={setSelectedHostelId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose hostel" />
                </SelectTrigger>
                <SelectContent>
                  {ownerHostels.map((hostel) => (
                    <SelectItem key={hostel.id} value={hostel.id}>
                      {hostel.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Filter by Floor</label>
              <Select value={selectedFloorId} onValueChange={setSelectedFloorId}>
                <SelectTrigger>
                  <SelectValue placeholder="All floors" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Floors</SelectItem>
                  {hostelFloors.map((floor) => (
                    <SelectItem key={floor.id} value={floor.id}>
                      {floor.floorName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Filter by Status</label>
              <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="available">Available</SelectItem>
                  <SelectItem value="occupied">Occupied</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                  <SelectItem value="reserved">Reserved</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search beds, rooms..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Occupancy Summary */}
      {occupancySummary && selectedHostel && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <BedIcon className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Beds</p>
                  <p className="text-2xl font-bold text-blue-600">{occupancySummary.totalBeds}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Occupied</p>
                  <p className="text-2xl font-bold text-green-600">{occupancySummary.occupiedBeds}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Available</p>
                  <p className="text-2xl font-bold text-blue-600">{occupancySummary.availableBeds}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Wrench className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Maintenance</p>
                  <p className="text-2xl font-bold text-yellow-600">{occupancySummary.maintenanceBeds}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-gradient-to-r from-blue-500 to-green-500 rounded-full" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Occupancy</p>
                  <p className="text-2xl font-bold text-purple-600">{occupancySummary.occupancyRate.toFixed(1)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Room and Bed Layout */}
      {selectedHostelId && (
        <Tabs defaultValue="floor-view" className="space-y-4">
          <TabsList>
            <TabsTrigger value="floor-view">Floor View</TabsTrigger>
            <TabsTrigger value="bed-list">Bed List</TabsTrigger>
            <TabsTrigger value="drag-drop">Drag & Drop</TabsTrigger>
            <TabsTrigger value="history">History & Events</TabsTrigger>
          </TabsList>

          <TabsContent value="floor-view" className="space-y-6">
            {hostelFloors.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Home className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Floors Found</h3>
                  <p className="text-muted-foreground mb-4">
                    Please add floors and rooms to manage bed allocations
                  </p>
                  <Button onClick={() => window.location.href = '/owner/floors-rooms'}>
                    <Home className="h-4 w-4 mr-2" />
                    Manage Floors & Rooms
                  </Button>
                </CardContent>
              </Card>
            ) : (
              hostelFloors.map((floor) => {
                const floorRooms = filteredRooms.filter(room => room.floorId === floor.id);
                if (floorRooms.length === 0 && selectedFloorId !== 'all') return null;
                
                return (
                  <FloorView 
                    key={floor.id}
                    floor={floor}
                    rooms={floorRooms}
                    onBedClick={handleBedClick}
                    getBedStatusColor={getBedStatusColor}
                    getBedStatusIcon={getBedStatusIcon}
                  />
                );
              })
            )}
          </TabsContent>

          <TabsContent value="bed-list">
            <BedListView
              beds={filteredBeds}
              rooms={hostelRooms}
              onBedClick={handleBedClick}
              getBedStatusColor={getBedStatusColor}
              getBedStatusIcon={getBedStatusIcon}
            />
          </TabsContent>

          <TabsContent value="drag-drop">
            <DragDropAllocationInterface
              hostelId={selectedHostelId}
              onAllocationRequest={async (bedId: string, tenant: HostelTenant) => {
                // Use the existing allocation handler
                const allocation = {
                  bedId,
                  tenantId: tenant.id,
                  checkInDate: new Date().toISOString().split('T')[0],
                  expectedCheckOutDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 6 months from now
                  monthlyRent: mockBeds.find(b => b.id === bedId)?.pricePerMonth || 0,
                  securityDeposit: (mockBeds.find(b => b.id === bedId)?.pricePerMonth || 0) * 2,
                  advancePayment: mockBeds.find(b => b.id === bedId)?.pricePerMonth || 0,
                  allocationNotes: `Allocated via drag-and-drop interface`
                };
                await handleBedAllocation(allocation);
              }}
              onBedClick={handleBedClick}
            />
          </TabsContent>

          <TabsContent value="history">
            <AllocationHistory hostelId={selectedHostelId} />
          </TabsContent>
        </Tabs>
      )}

      {/* Bed Details Dialog */}
      {selectedBed && (
        <BedDetailsDialog
          bed={selectedBed}
          tenant={selectedTenant}
          onClose={() => {
            setSelectedBed(null);
            setSelectedTenant(null);
          }}
          handleAllocateBed={handleAllocateBed}
          handleCheckOut={handleCheckOut}
          handleMaintenance={handleMaintenance}
        />
      )}

      {/* Allocation Modal */}
      {modalBed && (
        <BedAllocationModal
          bed={modalBed}
          isOpen={isAllocationModalOpen}
          onClose={() => {
            setIsAllocationModalOpen(false);
            setModalBed(null);
          }}
          onAllocate={handleBedAllocation}
        />
      )}

      {/* Check Out Modal */}
      {modalBed && (
        <CheckOutModal
          bed={modalBed}
          isOpen={isCheckOutModalOpen}
          onClose={() => {
            setIsCheckOutModalOpen(false);
            setModalBed(null);
          }}
          onCheckOut={handleBedCheckOut}
        />
      )}

      {/* Maintenance Modal */}
      {modalBed && (
        <MaintenanceModal
          bed={modalBed}
          isOpen={isMaintenanceModalOpen}
          onClose={() => {
            setIsMaintenanceModalOpen(false);
            setModalBed(null);
          }}
          onUpdateMaintenance={handleMaintenanceUpdate}
        />
      )}
    </div>
  );
};

// Floor View Component
interface FloorViewProps {
  floor: Floor;
  rooms: Room[];
  onBedClick: (bed: Bed) => void;
  getBedStatusColor: (status: Bed['status']) => string;
  getBedStatusIcon: (status: Bed['status']) => React.ReactNode;
}

const FloorView: React.FC<FloorViewProps> = ({
  floor,
  rooms,
  onBedClick,
  getBedStatusColor,
  getBedStatusIcon
}) => {
  // Determine responsive grid classes for beds based on bed count
  const getBedGridClasses = (bedCount: number) => {
    if (bedCount === 1) {
      return 'grid-cols-1';
    } else if (bedCount === 2) {
      return 'grid-cols-1 sm:grid-cols-2';
    } else if (bedCount <= 4) {
      return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-2';
    } else if (bedCount <= 6) {
      return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
    } else {
      return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Home className="h-5 w-5" />
          {floor.floorName}
          <Badge variant="secondary">{rooms.length} rooms</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
          {rooms.map((room) => {
            const roomBeds = getBedsByRoomId(room.id);
            const occupancyStatus = getRoomOccupancyStatus(room.id);

            return (
              <Card key={room.id} className="border-2 h-fit">
                <CardHeader className="pb-4">
                  {/* Room Title and Status */}
                  <div className="flex items-start justify-between gap-3 mb-3">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg sm:text-xl font-bold mb-1">{room.roomNumber}</CardTitle>
                      <p className="text-sm text-muted-foreground capitalize">
                        {room.roomType} • ₹{room.pricePerBed.toLocaleString()}/month
                      </p>
                    </div>
                    <div className="flex flex-col gap-2 items-end">
                      <Badge variant="outline" className="text-xs font-medium">
                        {occupancyStatus.occupiedBeds}/{occupancyStatus.totalBeds} beds
                      </Badge>
                      <Badge
                        variant="outline"
                        className={`text-xs font-medium ${
                          occupancyStatus.occupancyRate === 100
                            ? 'bg-red-100 text-red-800 border-red-200'
                            : occupancyStatus.occupancyRate > 50
                            ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                            : 'bg-green-100 text-green-800 border-green-200'
                        }`}
                      >
                        {occupancyStatus.occupancyRate.toFixed(0)}%
                      </Badge>
                    </div>
                  </div>

                  {/* Occupancy Progress Bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Occupancy Status</span>
                      <span>{occupancyStatus.occupiedBeds} of {occupancyStatus.totalBeds} occupied</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all ${
                          occupancyStatus.occupancyRate === 100
                            ? 'bg-red-500'
                            : occupancyStatus.occupancyRate > 50
                            ? 'bg-yellow-500'
                            : 'bg-green-500'
                        }`}
                        style={{ width: `${occupancyStatus.occupancyRate}%` }}
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className={`grid gap-3 sm:gap-4 ${getBedGridClasses(roomBeds.length)}`}>
                    {roomBeds.map((bed) => {
                      const tenant = bed.currentTenantId ? getHostelTenantById(bed.currentTenantId) : null;

                      return (
                        <div
                          key={bed.id}
                          onClick={() => onBedClick(bed)}
                          className={`
                            p-4 sm:p-5 rounded-xl border-2 cursor-pointer transition-all hover:shadow-lg hover:-translate-y-1
                            min-h-[200px] sm:min-h-[220px] flex flex-col
                            ${getBedStatusColor(bed.status)}
                          `}
                        >
                          {/* Bed Header */}
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <span className="font-bold text-base sm:text-lg">{bed.bedNumber}</span>
                              <Badge variant="outline" className="text-xs font-medium capitalize px-2 py-1">
                                {bed.status}
                              </Badge>
                            </div>
                            <div className="flex-shrink-0">
                              {getBedStatusIcon(bed.status)}
                            </div>
                          </div>

                          {/* Bed Type and Price */}
                          <div className="mb-4 pb-3 border-b border-gray-100">
                            <div className="flex items-center justify-between">
                              <p className="text-sm text-muted-foreground capitalize font-medium">
                                {bed.bedType.replace('_', ' ')}
                              </p>
                              <p className="text-sm font-bold text-primary">
                                ₹{bed.pricePerMonth.toLocaleString()}/mo
                              </p>
                            </div>
                          </div>

                          {/* Tenant Information for Occupied Beds */}
                          {tenant && bed.status === 'occupied' && (
                            <div className="flex-grow">
                              <div className="bg-white rounded-lg border p-4 space-y-3">
                                {/* Tenant Profile */}
                                <div className="flex items-center gap-3">
                                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                                    <span className="text-sm font-bold text-blue-700">
                                      {tenant.firstName.charAt(0)}{tenant.lastName.charAt(0)}
                                    </span>
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <p className="font-semibold text-sm leading-tight">
                                      {tenant.firstName} {tenant.lastName}
                                    </p>
                                    <p className="text-xs text-muted-foreground capitalize">
                                      {tenant.occupation?.replace('_', ' ') || 'Tenant'}
                                    </p>
                                  </div>
                                </div>

                                {/* Contact Information */}
                                <div className="space-y-2">
                                  <div className="flex items-center gap-2">
                                    <div className="w-4 h-4 rounded bg-gray-100 flex items-center justify-center">
                                      <span className="text-xs">📱</span>
                                    </div>
                                    <p className="text-xs font-medium">
                                      {tenant.phone}
                                    </p>
                                  </div>

                                  {bed.checkInDate && (
                                    <div className="flex items-center gap-2">
                                      <div className="w-4 h-4 rounded bg-gray-100 flex items-center justify-center">
                                        <span className="text-xs">📅</span>
                                      </div>
                                      <p className="text-xs text-muted-foreground">
                                        Since: {new Date(bed.checkInDate).toLocaleDateString('en-IN', {
                                          day: '2-digit',
                                          month: 'short',
                                          year: 'numeric'
                                        })}
                                      </p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Maintenance Info */}
                          {bed.status === 'maintenance' && (
                            <div className="flex-grow">
                              <div className="bg-yellow-50 rounded-lg border border-yellow-200 p-4 space-y-2">
                                <div className="flex items-center gap-2">
                                  <div className="w-5 h-5 rounded bg-yellow-200 flex items-center justify-center">
                                    <span className="text-xs">🔧</span>
                                  </div>
                                  <p className="text-sm font-semibold text-yellow-800">Maintenance Required</p>
                                </div>
                                {bed.maintenanceNotes && (
                                  <p className="text-xs text-yellow-700 leading-relaxed pl-7">
                                    {bed.maintenanceNotes}
                                  </p>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Available Bed Placeholder */}
                          {bed.status === 'available' && (
                            <div className="flex-grow flex items-center justify-center">
                              <div className="text-center py-4">
                                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-2">
                                  <span className="text-lg">✅</span>
                                </div>
                                <p className="text-sm font-medium text-green-700">Available</p>
                                <p className="text-xs text-muted-foreground">Ready for allocation</p>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>

                  {/* Show message if no beds */}
                  {roomBeds.length === 0 && (
                    <div className="text-center py-4 text-muted-foreground">
                      <BedIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-xs">No beds in this room</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>

        {rooms.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            No rooms found on this floor
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Bed List View Component
interface BedListViewProps {
  beds: Bed[];
  rooms: Room[];
  onBedClick: (bed: Bed) => void;
  getBedStatusColor: (status: Bed['status']) => string;
  getBedStatusIcon: (status: Bed['status']) => React.ReactNode;
}

const BedListView: React.FC<BedListViewProps> = (props) => {
  const { beds, rooms, onBedClick, getBedStatusColor, getBedStatusIcon } = props;

  return (
    <Card>
      <CardHeader>
        <CardTitle>All Beds</CardTitle>
        <CardDescription>
          Complete list of beds with current status and tenant information
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
          {beds.map((bed) => {
            const room = rooms.find(r => r.id === bed.roomId);
            const tenant = bed.currentTenantId ? getHostelTenantById(bed.currentTenantId) : null;

            return (
              <div
                key={bed.id}
                onClick={() => onBedClick(bed)}
                className={`
                  p-4 sm:p-5 rounded-xl border-2 cursor-pointer transition-all hover:shadow-lg hover:-translate-y-1
                  min-h-[180px] sm:min-h-[200px] flex flex-col
                  ${getBedStatusColor(bed.status)}
                `}
              >
                {/* Bed Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-2">
                      <p className="font-bold text-base sm:text-lg">{bed.bedNumber}</p>
                      <Badge variant="outline" className="text-xs font-medium capitalize px-2 py-1">
                        {bed.status}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Room {room?.roomNumber} • Floor {room?.floorId}
                    </p>
                  </div>
                  <div className="flex-shrink-0 ml-2">
                    {getBedStatusIcon(bed.status)}
                  </div>
                </div>

                {/* Bed Type and Price */}
                <div className="mb-4 pb-3 border-b border-gray-100">
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-muted-foreground capitalize font-medium">
                      {bed.bedType.replace('_', ' ')}
                    </p>
                    <p className="text-sm font-bold text-primary">
                      ₹{bed.pricePerMonth.toLocaleString()}/mo
                    </p>
                  </div>
                </div>

                {/* Tenant Information for Occupied Beds */}
                {tenant && bed.status === 'occupied' && (
                  <div className="flex-grow">
                    <div className="bg-white rounded-lg border p-3 space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                          <span className="text-xs font-bold text-blue-700">
                            {tenant.firstName.charAt(0)}{tenant.lastName.charAt(0)}
                          </span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-sm leading-tight">
                            {tenant.firstName} {tenant.lastName}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {tenant.phone}
                          </p>
                        </div>
                      </div>
                      {bed.checkInDate && (
                        <div className="flex items-center gap-2 pl-10">
                          <div className="w-3 h-3 rounded bg-gray-100 flex items-center justify-center">
                            <span className="text-xs">📅</span>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Since: {new Date(bed.checkInDate).toLocaleDateString('en-IN', {
                              day: '2-digit',
                              month: 'short',
                              year: 'numeric'
                            })}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Maintenance Info */}
                {bed.status === 'maintenance' && (
                  <div className="flex-grow">
                    <div className="bg-yellow-50 rounded-lg border border-yellow-200 p-3 space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded bg-yellow-200 flex items-center justify-center">
                          <span className="text-xs">🔧</span>
                        </div>
                        <p className="text-sm font-semibold text-yellow-800">Maintenance Required</p>
                      </div>
                      {bed.maintenanceNotes && (
                        <p className="text-xs text-yellow-700 leading-relaxed pl-6">
                          {bed.maintenanceNotes}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {/* Available Bed Placeholder */}
                {bed.status === 'available' && (
                  <div className="flex-grow flex items-center justify-center">
                    <div className="text-center py-2">
                      <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-1">
                        <span className="text-base">✅</span>
                      </div>
                      <p className="text-sm font-medium text-green-700">Available</p>
                      <p className="text-xs text-muted-foreground">Ready for allocation</p>
                    </div>
                  </div>
                )}
                </div>
              </div>
            );
          })}
        </div>

        {beds.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <BedIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No beds found matching your criteria</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Bed Details Dialog Component
interface BedDetailsDialogProps {
  bed: Bed;
  tenant: HostelTenant | null;
  onClose: () => void;
  handleAllocateBed: (bed: Bed) => void;
  handleCheckOut: (bed: Bed) => void;
  handleMaintenance: (bed: Bed) => void;
}

const BedDetailsDialog: React.FC<BedDetailsDialogProps> = ({
  bed,
  tenant,
  onClose,
  handleAllocateBed,
  handleCheckOut,
  handleMaintenance
}) => {
  const room = mockRooms.find(r => r.id === bed.roomId);
  const floor = room ? mockFloors.find(f => f.id === room.floorId) : null;
  const allocation = getAllocationByBedId(bed.id);

  const getBedStatusColor = (status: Bed['status']) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'occupied':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'reserved':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BedIcon className="h-5 w-5" />
            Bed {bed.bedNumber} Details
          </DialogTitle>
          <DialogDescription>
            {room?.roomNumber} • {floor?.floorName}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Bed Information */}
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Bed Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Status:</span>
                  <Badge className={getBedStatusColor(bed.status)}>
                    {bed.status.charAt(0).toUpperCase() + bed.status.slice(1)}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Type:</span>
                  <span className="text-sm capitalize">{bed.bedType.replace('_', ' ')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Size:</span>
                  <span className="text-sm capitalize">{bed.bedSize}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Price:</span>
                  <span className="text-sm font-medium">₹{bed.pricePerMonth.toLocaleString()}/month</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Amenities</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center gap-2">
                  <CheckCircle className={`h-4 w-4 ${bed.hasStorage ? 'text-green-600' : 'text-gray-400'}`} />
                  <span className="text-sm">Storage</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className={`h-4 w-4 ${bed.hasPrivacyCurtain ? 'text-green-600' : 'text-gray-400'}`} />
                  <span className="text-sm">Privacy Curtain</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className={`h-4 w-4 ${bed.hasReadingLight ? 'text-green-600' : 'text-gray-400'}`} />
                  <span className="text-sm">Reading Light</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className={`h-4 w-4 ${bed.hasPowerOutlet ? 'text-green-600' : 'text-gray-400'}`} />
                  <span className="text-sm">Power Outlet</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tenant Information */}
          {tenant && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Current Tenant
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Name</p>
                    <p className="font-medium">{tenant.firstName} {tenant.lastName}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Occupation</p>
                    <p className="capitalize">{tenant.occupation.replace('_', ' ')}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Phone</p>
                    <p>{tenant.phone}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Email</p>
                    <p className="text-sm">{tenant.email}</p>
                  </div>
                </div>

                {allocation && (
                  <div className="pt-4 border-t">
                    <h4 className="font-medium mb-3">Allocation Details</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Check-in Date</p>
                        <p className="font-medium">{new Date(allocation.checkInDate).toLocaleDateString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Expected Check-out</p>
                        <p className="font-medium">{new Date(allocation.expectedCheckOutDate).toLocaleDateString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Monthly Rent</p>
                        <p className="font-medium">₹{allocation.monthlyRent.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Security Deposit</p>
                        <p className="font-medium">₹{allocation.securityDeposit.toLocaleString()}</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            {bed.status === 'available' && (
              <Button
                className="flex-1"
                onClick={() => {
                  onClose();
                  handleAllocateBed(bed);
                }}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Allocate Bed
              </Button>
            )}
            {bed.status === 'occupied' && (
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => {
                  onClose();
                  handleCheckOut(bed);
                }}
              >
                <UserMinus className="h-4 w-4 mr-2" />
                Check Out
              </Button>
            )}
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => {
                onClose();
                handleMaintenance(bed);
              }}
            >
              <Wrench className="h-4 w-4 mr-2" />
              {bed.status === 'maintenance' ? 'Update Maintenance' : 'Mark for Maintenance'}
            </Button>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
